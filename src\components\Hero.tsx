import { ArrowR<PERSON>, Award, Shield, Users } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import heroBackground from '@/assets/hero-background.jpg';

const Hero = () => {
  return (
    <section id="home" className="relative min-h-[100dvh] sm:min-h-screen flex items-center justify-center overflow-hidden pt-16 sm:pt-0">
      {/* Background Image with Overlay */}
      <div
        className="absolute inset-0 bg-cover bg-center bg-no-repeat"
        style={{ backgroundImage: `url(${heroBackground})` }}
      >
        <div className="absolute inset-0 bg-gradient-hero opacity-85"></div>
      </div>

      {/* Content */}
      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center py-8 sm:py-0">
        <div className="animate-fade-in">
          {/* Main Heading */}
          <h1 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-bold text-white mb-4 sm:mb-6 leading-tight">
            Engineering
            <span className="block bg-gradient-to-r from-primary-light to-accent bg-clip-text text-transparent">
              Excellence
            </span>
            in MEP Solutions
          </h1>

          {/* Subtitle */}
          <p className="text-base sm:text-lg md:text-xl lg:text-2xl text-gray-200 mb-6 sm:mb-8 max-w-3xl mx-auto leading-relaxed">
            Nile Pro delivers comprehensive MEP installation works combining advanced technology
            with durable design for optimal performance in every environment.
          </p>

          {/* CTA Buttons */}
          <div className="flex flex-col sm:flex-row gap-3 sm:gap-4 justify-center items-center mb-8 sm:mb-16">
            <Button size="lg" className="w-full sm:w-auto bg-gradient-primary hover:opacity-90 shadow-primary text-sm sm:text-base lg:text-lg px-6 sm:px-8 py-3 sm:py-4 min-h-[44px]">
              Discover Our Services
              <ArrowRight className="ml-2 h-4 w-4 sm:h-5 sm:w-5" />
            </Button>
            <Button size="lg" className="w-full sm:w-auto bg-white/10 text-white border-2 border-accent hover:bg-accent hover:text-white backdrop-blur-sm transition-all duration-300 text-sm sm:text-base lg:text-lg px-6 sm:px-8 py-3 sm:py-4 min-h-[44px]">
              View Projects
            </Button>
          </div>

          {/* Stats */}
          <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 sm:gap-6 lg:gap-8 max-w-4xl mx-auto">
            <div className="text-center animate-slide-in-right" style={{ animationDelay: '0.2s' }}>
              <div className="flex justify-center mb-3 sm:mb-4">
                <div className="p-2 sm:p-3 bg-primary/20 rounded-full">
                  <Award className="h-5 w-5 sm:h-6 sm:w-6 lg:h-8 lg:w-8 text-primary-light" />
                </div>
              </div>
              <div className="text-xl sm:text-2xl lg:text-3xl font-bold text-white mb-1 sm:mb-2">15+</div>
              <div className="text-xs sm:text-sm lg:text-base text-gray-300">Years Experience</div>
            </div>

            <div className="text-center animate-slide-in-right" style={{ animationDelay: '0.4s' }}>
              <div className="flex justify-center mb-3 sm:mb-4">
                <div className="p-2 sm:p-3 bg-primary/20 rounded-full">
                  <Shield className="h-5 w-5 sm:h-6 sm:w-6 lg:h-8 lg:w-8 text-primary-light" />
                </div>
              </div>
              <div className="text-xl sm:text-2xl lg:text-3xl font-bold text-white mb-1 sm:mb-2">500+</div>
              <div className="text-xs sm:text-sm lg:text-base text-gray-300">Projects Completed</div>
            </div>

            <div className="text-center animate-slide-in-right" style={{ animationDelay: '0.6s' }}>
              <div className="flex justify-center mb-3 sm:mb-4">
                <div className="p-2 sm:p-3 bg-primary/20 rounded-full">
                  <Users className="h-5 w-5 sm:h-6 sm:w-6 lg:h-8 lg:w-8 text-primary-light" />
                </div>
              </div>
              <div className="text-xl sm:text-2xl lg:text-3xl font-bold text-white mb-1 sm:mb-2">100%</div>
              <div className="text-xs sm:text-sm lg:text-base text-gray-300">Client Satisfaction</div>
            </div>
          </div>
        </div>
      </div>

      {/* Scroll Indicator - Hidden on mobile, visible on larger screens */}
      <div className="hidden sm:block absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-float">
        <div className="w-6 h-10 border-2 border-white/70 rounded-full flex justify-center hover:border-white transition-colors duration-300">
          <div className="w-1 h-3 bg-white/70 rounded-full mt-2 animate-bounce"></div>
        </div>
      </div>
    </section>
  );
};

export default Hero;