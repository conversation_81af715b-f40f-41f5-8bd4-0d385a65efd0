import { CheckCircle, Target, Eye, Lightbulb, Award, Shield, Users, Zap } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';

const About = () => {
  const features = [
    {
      icon: <Target className="h-7 w-7" />,
      title: "Precision Engineering",
      description: "Every project is executed with meticulous attention to detail and engineering excellence, ensuring optimal performance and longevity.",
      highlight: "99.8% Accuracy Rate"
    },
    {
      icon: <Shield className="h-7 w-7" />,
      title: "Quality Assurance",
      description: "Rigorous quality control processes and international certifications ensure the highest standards in all our installations.",
      highlight: "ISO 9001 Certified"
    },
    {
      icon: <Lightbulb className="h-7 w-7" />,
      title: "Innovative Solutions",
      description: "We leverage cutting-edge technology and smart building systems to deliver future-ready MEP solutions.",
      highlight: "Smart Technology"
    },
    {
      icon: <Zap className="h-7 w-7" />,
      title: "Energy Efficiency",
      description: "Sustainable and energy-efficient designs that reduce operational costs while maintaining optimal performance.",
      highlight: "30% Energy Savings"
    }
  ];

  const achievements = [
    "15+ years of industry expertise and innovation",
    "500+ successful project completions across sectors",
    "Certified MEP installation specialists and engineers",
    "24/7 maintenance and technical support services",
    "Energy-efficient and sustainable system designs",
    "Full compliance with international safety standards"
  ];

  const stats = [
    {
      icon: <Award className="h-8 w-8" />,
      number: "15+",
      label: "Years Experience",
      description: "Industry Leadership"
    },
    {
      icon: <Shield className="h-8 w-8" />,
      number: "500+",
      label: "Projects Completed",
      description: "Successful Installations"
    },
    {
      icon: <Users className="h-8 w-8" />,
      number: "100%",
      label: "Client Satisfaction",
      description: "Quality Guarantee"
    }
  ];

  return (
    <section id="about" className="py-20 bg-gradient-to-br from-background via-primary/5 to-accent/5 relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute top-20 left-10 w-72 h-72 bg-primary rounded-full blur-3xl"></div>
        <div className="absolute bottom-20 right-10 w-96 h-96 bg-accent rounded-full blur-3xl"></div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        {/* Section Header */}
        <div className="text-center mb-20">
          <div className="inline-flex items-center px-4 py-2 bg-primary/10 rounded-full mb-6">
            <span className="text-primary font-semibold text-sm uppercase tracking-wider">About Our Company</span>
          </div>
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-foreground mb-6 leading-tight">
            About <span className="bg-gradient-primary bg-clip-text text-transparent">Nile Pro</span>
          </h2>
          <p className="text-xl text-muted-foreground max-w-4xl mx-auto leading-relaxed">
            Leading MEP construction specialists dedicated to delivering exceptional
            mechanical, electrical, and plumbing solutions for commercial and industrial projects
            with a commitment to innovation, quality, and sustainability.
          </p>
        </div>

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 lg:gap-16 items-center mb-20">
          {/* Left Content */}
          <div className="space-y-8">
            <div className="space-y-6">
              <h3 className="text-2xl md:text-3xl lg:text-4xl font-bold text-foreground leading-tight">
                Engineering Excellence Since
                <span className="block bg-gradient-primary bg-clip-text text-transparent">2009</span>
              </h3>

              <p className="text-lg text-muted-foreground leading-relaxed">
                Nile Pro for Construction has established itself as a premier MEP installation contractor,
                combining advanced technology with durable design principles. Our comprehensive approach
                ensures optimal comfort, efficiency, and performance in every environment we serve.
              </p>

              <p className="text-lg text-muted-foreground leading-relaxed">
                From complex commercial buildings to industrial facilities, we deliver integrated
                MEP solutions that exceed expectations while maintaining the highest safety and
                quality standards throughout the project lifecycle.
              </p>
            </div>

            {/* Achievements List */}
            <div className="space-y-4">
              <h4 className="text-xl font-semibold text-foreground mb-4">Our Key Strengths</h4>
              <div className="grid grid-cols-1 gap-3">
                {achievements.map((achievement, index) => (
                  <div key={index} className="flex items-center space-x-3 p-3 rounded-lg hover:bg-primary/5 transition-colors duration-200">
                    <CheckCircle className="h-5 w-5 text-primary flex-shrink-0" />
                    <span className="text-muted-foreground font-medium">{achievement}</span>
                  </div>
                ))}
              </div>
            </div>

            {/* CTA Button */}
            <div className="pt-4">
              <Button size="lg" className="bg-gradient-primary hover:opacity-90 shadow-lg">
                Learn More About Us
              </Button>
            </div>
          </div>

          {/* Right Content - Feature Cards */}
          <div className="space-y-6">
            {features.map((feature, index) => (
              <Card key={index} className="group hover:shadow-xl hover:shadow-primary/10 transition-all duration-300 border-border hover:border-primary/20 bg-card/50 backdrop-blur-sm">
                <CardContent className="p-6">
                  <div className="flex items-start space-x-5">
                    <div className="p-4 bg-gradient-primary rounded-xl shadow-lg group-hover:scale-110 transition-transform duration-300">
                      <div className="text-white">
                        {feature.icon}
                      </div>
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center justify-between mb-3">
                        <h4 className="text-xl font-semibold text-foreground group-hover:text-primary transition-colors">
                          {feature.title}
                        </h4>
                        <span className="text-xs font-semibold text-primary bg-primary/10 px-2 py-1 rounded-full">
                          {feature.highlight}
                        </span>
                      </div>
                      <p className="text-muted-foreground leading-relaxed">
                        {feature.description}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Stats Section */}
        <div className="mb-20">
          <div className="text-center mb-12">
            <h3 className="text-2xl md:text-3xl font-bold text-foreground mb-4">
              Our Track Record Speaks for Itself
            </h3>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Numbers that reflect our commitment to excellence and client satisfaction
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {stats.map((stat, index) => (
              <Card key={index} className="group text-center p-8 bg-gradient-to-br from-card to-card/50 border-primary/10 hover:border-primary/30 hover:shadow-xl hover:shadow-primary/10 transition-all duration-300">
                <CardContent className="p-0">
                  <div className="w-20 h-20 bg-gradient-primary rounded-full flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
                    <div className="text-white">
                      {stat.icon}
                    </div>
                  </div>
                  <div className="text-4xl font-bold text-foreground mb-2 bg-gradient-primary bg-clip-text text-transparent">
                    {stat.number}
                  </div>
                  <div className="text-xl font-semibold text-foreground mb-2">
                    {stat.label}
                  </div>
                  <div className="text-sm text-muted-foreground">
                    {stat.description}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Mission & Vision */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          <Card className="group border-primary/20 hover:border-primary/40 hover:shadow-xl hover:shadow-primary/10 transition-all duration-300 bg-gradient-to-br from-card to-primary/5">
            <CardContent className="p-8 text-center">
              <div className="w-20 h-20 bg-gradient-primary rounded-full flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
                <Target className="h-10 w-10 text-white" />
              </div>
              <h3 className="text-2xl font-bold text-foreground mb-4">Our Mission</h3>
              <p className="text-muted-foreground leading-relaxed">
                To deliver exceptional MEP solutions that combine innovation, quality, and reliability,
                ensuring our clients achieve optimal building performance, energy efficiency, and sustainable operations
                that contribute to a better future.
              </p>
            </CardContent>
          </Card>

          <Card className="group border-accent/20 hover:border-accent/40 hover:shadow-xl hover:shadow-accent/10 transition-all duration-300 bg-gradient-to-br from-card to-accent/5">
            <CardContent className="p-8 text-center">
              <div className="w-20 h-20 bg-gradient-accent rounded-full flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
                <Eye className="h-10 w-10 text-white" />
              </div>
              <h3 className="text-2xl font-bold text-foreground mb-4">Our Vision</h3>
              <p className="text-muted-foreground leading-relaxed">
                To be the leading MEP contractor recognized for engineering excellence,
                innovative solutions, and unwavering commitment to client satisfaction while setting
                new industry standards for sustainable construction practices.
              </p>
            </CardContent>
          </Card>
        </div>
      </div>
    </section>
  );
};

export default About;